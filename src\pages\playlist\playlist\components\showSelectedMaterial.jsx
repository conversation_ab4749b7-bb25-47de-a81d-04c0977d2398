/* eslint-disable */
import { useRef, useState, useEffect } from "react";
import { IconButton, Typography } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
// i18n
import { useTranslation } from "react-i18next";
import "./img.css";

const ShowSelectedMaterial = (props) => {
  const { playListMaterialList, windowWidthData, columsWidth } = props;
  const { t } = useTranslation();
  const typographyRef = useRef(null); // Ref for Typography element
  //监听屏幕宽度变化
  const [windowWidth, setWindowWidth] = useState(0);
  const [textHeight, setTextHeight] = useState(0);

  // 通过事件对象获取浏览器窗口的高度
  const resizeUpdate = (e) => {
    let getWidth = e.target.innerWidth;
    // console.log("屏幕宽度getWidth", getWidth);
    setWindowWidth(getWidth);
  };

  useEffect(() => {
    // 页面刚加载完成后获取浏览器窗口的大小
    let h = window.innerWidth;
    setWindowWidth(h);
    // 页面变化时获取浏览器窗口的大小
    window.addEventListener("resize", resizeUpdate);
    // Get the height of Typography element
    if (typographyRef.current) {
      setTextHeight(typographyRef.current.clientHeight);
    }
    return () => {
      // 组件销毁时移除监听事件
      window.removeEventListener("resize", resizeUpdate);
    };
  }, []);

  windowWidthData(windowWidth);

  //是否显示更多的清单列表
  const showMoreRef = useRef(false);
  const handleShowMore = (value) => {
    showMoreRef.current = value;
  };
  let sliceArray =
    playListMaterialList.length > 4
      ? playListMaterialList.slice(0, 4)
      : playListMaterialList;

  return (
    <>
      {playListMaterialList.length * 65 > columsWidth &&
      playListMaterialList.length > 4 ? (
        showMoreRef.current ? (
          <Typography
            className="textSpace"
            ref={typographyRef}
            // style={{
            //   maxHeight: showMoreRef.current ? "none" : `${textHeight}px`, // Set the max height based on content
            //   overflow: "hidden",
            // }}
          >
            {playListMaterialList.map((item, index) => (
              <>
                {(index + 1) % 4 === 1 && index + 1 != 1 ? <br /> : null}
                <ShowByType data={item} />
                {index === playListMaterialList.length - 1 && (
                  <IconButton
                    onClick={() => handleShowMore(false)}
                    sx={{
                      width: "40px", // 设置宽度
                      height: "30px", // 设置高度
                      position: "absolute",
                      right: "-35px",
                      bottom: "0",
                    }}
                  >
                    <ExpandLessIcon
                      sx={{
                        width: "16px", // 设置宽度
                        height: "16px", // 设置高度
                      }}
                    />
                    {/* <Typography sx={{ fontSize: 12 }}>
                      {t("common.common_form_packup")}
                    </Typography> */}
                  </IconButton>
                )}
              </>
            ))}
          </Typography>
        ) : (
          <Typography className="textSpace" noWrap>
            {sliceArray.map((item, index) => (
              <>
                <ShowByType data={item} />
                {index === sliceArray.length - 1 && (
                  <IconButton
                    onClick={() => handleShowMore(true)}
                    sx={{
                      width: "40px", // 设置宽度
                      height: "30px", // 设置高度
                      zIndex: 111111111111,
                    }}
                  >
                    <ExpandMoreIcon
                      sx={{
                        width: "16px", // 设置宽度
                        height: "16px", // 设置高度
                      }}
                    />
                    {/* <Typography sx={{ fontSize: 12 }}>
                      {t("common.common_more")}
                    </Typography> */}
                  </IconButton>
                )}
              </>
            ))}
          </Typography>
        )
      ) : (
        <Typography className="textSpace">
          {playListMaterialList.map((item, index) => (
            <ShowByType key={index} data={item} />
          ))}
        </Typography>
      )}
    </>
  );
};

function ShowByType(propos) {
  const { data } = propos;

  if (data.type === "image" || data.type == 'layout') {
    return (
      <>
        <img
          key={data.id}
          loading="lazy"
          className="img"
          alt=""
          style={{ margin: "5px 0 0 0" }}
          src={data.downloadUrl}
        />
        &nbsp;&nbsp;
      </>
    );
  }
  if (data.type === "media") {
    let coverImage = data.coverImage
      ? data.coverImage
      : "https://armatura-minervaiot-dev-ap-southeast-1-private.s3.amazonaws.com/public/D0374F17B13643898E5836E3AED90924/applications/CloudMedia/eb0a59a4-6a4e-4fa5-8ba5-8e6187128477.png";
    return (
      <>
        <img
          key={data.id}
          loading="lazy"
          className="img"
          alt=""
          style={{ margin: "5px 0 0 0" }}
          src={coverImage}
        />
        &nbsp;&nbsp;
      </>
    );
  }
  if (data.type === "audio") {
    return (
      <>
        <svg
          key={data.id}
          data-v-56e751f7=""
          t="1592532095422"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="2906"
          width="35"
          height="35"
          className="icon music-icon"
        >
          <path
            data-v-56e751f7=""
            d="M742.3 100.3l-25.6 44.3c126.2 73 204.7 208.9 204.7 354.6 0 225.7-183.6 409.3-409.3 409.3S102.8 724.8 102.8 499.1c0-145.7 78.4-281.5 204.7-354.6l-25.6-44.3c-142 82.1-230.2 235-230.2 398.8 0 253.9 206.6 460.5 460.5 460.5S972.6 753 972.6 499.1c0-163.9-88.2-316.7-230.3-398.8z"
            fill="#1296db"
            p-id="2907"
          ></path>{" "}
          <path
            data-v-56e751f7=""
            d="M464.2 437l-25.6-44.3c-45.3 26.2-73.5 75-73.5 127.3 0 81 65.9 147 147 147s147-65.9 147-147v-6.3L451.2 115.4h164V64.2H366.8l241 461.8c-3.1 50.1-44.8 89.9-95.6 89.9-52.8 0-95.8-43-95.8-95.8-0.1-34.1 18.2-66 47.8-83.1z"
            fill="#1296db"
            p-id="2908"
          ></path>
        </svg>
        &nbsp;&nbsp;
      </>
    );
  }
}

export default ShowSelectedMaterial;
