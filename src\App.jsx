import ThemeCustomization from "@/themes";
import React from "react";
import ScrollTop from "@/components/ScrollTop";

import { ConfirmProvider } from "@/components/zkconfirm";
// import { getStoreLang } from "@/src/utils/langUtils";
// 路由鉴权
import RouterWaiter from "@/components/routerWaiter";
import routes from "@/router/routers";
import onRouteBefore from "@/router/onRouteBefore";
import Loader from "@/components/Loader";

import { ToastContainer, Zoom, Slide } from "react-toastify";
import { useDispatch } from "react-redux";
import { setMenuList } from "@/store/reducers/menu";
import { getUserMenus } from "@/service/api/L3Sevice";
const App = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const menuRes = await getUserMenus({ applicationCode: "SD" });
        let menus = menuRes.data;
        dispatch(setMenuList(menus));
      } catch (error) {
        console.error("Error fetching user menus:", error);
      }
    };

    fetchData();
  }, []);
  return (
    <ThemeCustomization>
      <ConfirmProvider>
        <ScrollTop>
          <RouterWaiter
            routes={routes}
            loading={<Loader />}
            onRouteBefore={onRouteBefore}
          />

          <ToastContainer
            position="top-center"
            style={{
              fontSize: "16px",
            }}
            autoClose={2000}
            hideProgressBar
            newestOnTop={false}
            closeOnClick
            rtl={false}
            limit={3}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
            transition={Slide}
          />
        </ScrollTop>
      </ConfirmProvider>
    </ThemeCustomization>
  );
};

export default App;
