import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import { setToken } from "@/utils/auth";

import "@/lang/index";
import { Provider as ReduxProvider } from "react-redux";
import {
  renderWithQiankun,
  qiankunWindow,
} from "vite-plugin-qiankun/dist/helper";
import { store } from "@/store";
import { useDispatchUser } from "@/hooks/user";
import { BrowserRouter as Router } from "react-router-dom";
import "@/assets/css/global.less";
import "virtual:svg-icons-register";

const initQianKun = () => {
  renderWithQiankun({
    // 当前应用在主应用中的生命周期
    // 文档 https://qiankun.umijs.org/zh/guide/getting-started#
    mount(props) {
      try {
        render(props.container);

        props.onGlobalStateChange((state) => {
          if (state?.token) {
            setToken(state.token);
            sessionStorage.setItem(
              "USER_INFO",
              JSON.stringify(state?.userInfo)
            );

            // store.dispatch(
            //   useDispatchUser().stateSetPermission(state?.userInfo?.permissions)
            // );
          }
        }, true);
      } catch (error) {
        console.error("子应用挂载时发生错误:", error);
      }
    },
    bootstrap() {
      console.log("子应用正在初始化");
    },
    unmount() {
      // const { container } = props;
      // unmountUpdate(container);
    },
  });
};

// 在其他地方监听挂载完成事件
window.addEventListener("cms-app", () => {
  console.log("子应用挂载完成");
  // 执行挂载完成后的操作
});

const render = (container) => {
  // 如果是在主应用的环境下就挂载主应用的节点，否则挂载到本地
  const appDom = container ? container : document.getElementById("root");
  ReactDOM.createRoot(appDom).render(
    <ReduxProvider store={store}>
      <Router basename="/cms-app">
        <App />
      </Router>
    </ReduxProvider>
  );
};

if (!qiankunWindow.__POWERED_BY_QIANKUN__) {
  // 独立运行模式，HMR 直接生效
  render();
} else {
  initQianKun();
}

// 判断当前应用是否在主应用中
// qiankunWindow.__POWERED_BY_QIANKUN__ ? initQianKun() : render();
