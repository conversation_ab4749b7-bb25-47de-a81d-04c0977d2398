import {
  Grid,
  Card,
  InputAdornment,
  IconButton,
  Button,
  TextField,
  Typography,
  Stack,
  CircularProgress,
  Box,
} from "@mui/material";
import React, { forwardRef, useRef, useEffect, useState } from "react";
import GradientBox from "@/components/GradientBox";
import addIcon from "@/assets/images/icons/add.svg";
import locationIcon from "@/assets/images/icons/location.svg";
import OutletPercent from "./OutletPercent";
import "./outletDetails.less";
import RegionSelect from "@/components/RegionSelect";
import { boxStyle, boxItem, iconSty } from "./styleConfig";
import OutletChart from "./OutletChart";

import {
  getOutletComparison,
  getOutletTotal,
} from "@/service/api/dashboardOutlet";
import { useTranslation } from "react-i18next";

const OutletDetails = (props) => {
  const [areaId, setAreaId] = useState("");
  const [regionError, setRegionError] = useState("");
  const { t } = useTranslation();
  const [newScreenTotal, setNewScreenTotal] = useState(0);
  const [screenTotal, setScreenTotal] = useState(0);
  const [chartData, setChartData] = useState([]);
  const regionChange = (e) => {
    setRegionError("");
    if (e) {
      let id = e.id;
      setAreaId(id);
    } else {
      setAreaId("");
    }
  };

  const regionClear = () => {
    setAreaId("");
    setRegionError("");
  };

  const loadTotal = () => {
    getOutletTotal({
      retailClientId: props.retailClientId,
    })
      .then((res) => {
        if (res.code === 0) {
          let data = res.data;
          setNewScreenTotal(data.newScreenTotal);
          setScreenTotal(data.screenTotal);
        } else {
          setNewScreenTotal(0);
          setScreenTotal(0);
        }
      })
      .catch((e) => {});
  };

  useEffect(() => {
    if (props.retailClientId) {
      loadTotal();
    } else {
      setNewScreenTotal(0);
      setScreenTotal(0);
    }
  }, [props.retailClientId]);

  const loadBarChart = () => {
    getOutletComparison({
      retailClientId: props.retailClientId,
      areaId: areaId,
    })
      .then((res) => {
        if (res.code === 0) {
          let data = res.data;
          setChartData(data);
        } else {
          setChartData([]);
        }
      })
      .catch((e) => {
        setChartData([]);
      });
  };

  useEffect(() => {
    let areaData = localStorage.getItem("outletSelectedRegionValue");
    if (areaData) {
      const area = JSON.parse(areaData);
      setAreaId(area.id);
    }
    if (props.retailClientId && areaId) {
      loadBarChart();
    } else {
      setChartData([]);
    }
  }, [props.retailClientId, areaId]);

  return (
    <Grid style={{ mb: 2 }}>
      <Grid container>
        <GradientBox style={boxStyle}>
          <Grid style={boxItem} container>
            <Grid>
              <OutletPercent retailClientId={props.retailClientId} />
            </Grid>
            <Grid sx={{ ml: 2 }}>
              <Typography
                style={{
                  fontSize: "20px",
                  fontWeight: "600",
                  color: "rgb(112,112,112)",
                }}>
                {t("outlet.total_outlets_installed_p")}
              </Typography>
            </Grid>
          </Grid>
        </GradientBox>
        <GradientBox style={boxStyle}>
          <Grid style={boxItem} container>
            <Grid sx={iconSty}>
              <img
                style={{ width: "100%", height: "100%" }}
                src={locationIcon}
              />
            </Grid>
            <Grid sx={{ ml: 2 }}>
              <Typography className={"textStyle"}>{screenTotal}</Typography>
              <Typography>{t("outlet.total_outlets_installed")}</Typography>
            </Grid>
          </Grid>
        </GradientBox>
        <GradientBox style={boxStyle}>
          <Grid style={boxItem} container>
            <Grid sx={iconSty}>
              <img style={{ width: "100%", height: "100%" }} src={addIcon} />
            </Grid>
            <Grid sx={{ ml: 2 }}>
              <Typography className={"textStyle"}>{newScreenTotal}</Typography>
              <Typography> {t("outlet.new_outlets_installed")}</Typography>
              <Typography> {t("outlet.year_to_date")} </Typography>
            </Grid>
          </Grid>
        </GradientBox>
      </Grid>
      <Grid>
        <Grid
          sx={{ mt: 2, ml: 3 }}
          style={{
            width: "300px",
          }}>
          <RegionSelect
            regionKey="outletSelectedRegionValue"
            label={t("dashboard.region")}
            onClear={regionClear}
            error={regionError}
            onChange={regionChange}
          />
        </Grid>
        <Grid
          sx={{
            mt: 3,
          }}
          style={{
            height: "500px",
          }}>
          {chartData?.length > 0 && (
            <OutletChart chartData={chartData}></OutletChart>
          )}
        </Grid>
      </Grid>
    </Grid>
  );
};
export default OutletDetails;
