import React from "react";
/* eslint-disable react-hooks/rules-of-hooks */
import { useEffect, useState } from "react";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { getInfo } from "@/service/api/totaloutlet";
import { useFormik } from "formik";
import * as Yup from "yup";
import LoadingButton from "@mui/lab/LoadingButton";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
// 消息提示
import { toast } from "react-toastify";
import dayjs from "dayjs";
import { isEmpty } from "lodash-es";
import {
  FormHelperText,
  Grid,
  InputLabel,
  TextField,
  OutlinedInput,
  Stack,
  Typography,
} from "@mui/material";
import Treeselect from "@/components/zktreeselect";
import ZKSelect from "@/components/ZKSelect";
import { useTranslation } from "react-i18next";

import "dayjs/locale/zh-cn";
import "dayjs/locale/es";
import "dayjs/locale/en";
import { getAreaTree } from "@/service/api/L3Sevice.js";
const totaloutletForm = ({
  id = undefined,
  open = false,
  loading = false,
  // areaOption = [],
  merchantOptions = [],
  onSave = (values) => {},
  onCancel = (values) => {},
}) => {
  const { i18n, t } = useTranslation();
  const titles = [t("common.common_title_add"), t("common.common_title_edit")];
  const [title, setTitle] = useState(titles[0]);
  const treeSelectRef = React.useRef(null);
  const [areaData, setAreaData] = React.useState([]);

  const [currentLocale, setCurrentLocale] = useState("en");

  useEffect(() => {
    const currentLanguage = i18n.language;
    // console.log(currentLanguage)
    if (currentLanguage === "zh") {
      setCurrentLocale("zh-cn");
      dayjs.locale("zh-cn");
    } else if (currentLanguage === "en") {
      setCurrentLocale("en");
      dayjs.locale("en");
    } else {
      setCurrentLocale(currentLanguage);
      dayjs.locale(currentLanguage);
    }
  }, []);

  //获取区域
  const getAreaData = (merchantId) => {
    getAreaTree(merchantId).then((res) => {
      setAreaData(res.data);
    });
  };
  useEffect(() => {
    totalOutletForm.resetForm();
    // 每次进来需要重置一下表单的操作
    totalOutletForm.setFieldValue({
      id: undefined,
      merchantId: undefined,
      totalOutlet: 0,
      areaId: undefined,
      yearAndMonth: undefined,
    });
    if (id) {
      setTitle(titles[1]);
      handleGetFormData(id);
    }
  }, [id, open]);

  const handleGetFormData = (id) => {
    getInfo(id).then((res) => {
      const { data } = res;
      setAreaData([]);
      getAreaData(data?.departmentId);
      totalOutletForm.setValues({
        id: id,
        departmentId: String(data?.departmentId),
        areaId: String(data?.areaId),
        yearAndMonth: dayjs(`${data?.year}-${data?.month}`),
        totalOutlet: data?.totalOutlet,
      });
      treeSelectRef.current.setItem({
        id: data.areaId,
        name: data.areaName,
      });
    });
  };

  const totalOutletForm = useFormik({
    initialValues: {
      id: undefined,
      areaId: "",
      yearAndMonth: undefined,
      totalOutlet: 0,
      departmentId: "",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handleSave(values);
        // handleSaveMerchant(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validationSchema: Yup.object().shape({
      departmentId: Yup.string().required(t("ips.ips_select_merchant")),
      areaId: Yup.string().required(t("ips.ips_enter_region")),
      yearAndMonth: Yup.mixed().test({
        name: "yearAndMonth",
        test(value, ctx) {
          if (typeof value === "string") {
            if (isEmpty(value) || value === "Invalid Date") {
              return ctx.createError({
                message: t("common.common_please_year_and_month"),
              });
            }
          } else {
            if (value === undefined) {
              return ctx.createError({
                message: t("common.common_please_year_and_month"),
              });
            }
          }
          return true;
        },
      }),
      totalOutlet: Yup.number()
        .min(1, t("common.common_total_min_0"))
        .required(t("common.common_total_please")),
    }),
  });

  const handleSave = (values) => {
    // 处理数据
    const formValues = { ...values };
    let date = dayjs(values.yearAndMonth).format("YYYY-MM");
    formValues.year = date.split("-")[0];
    formValues.month = date.split("-")[1];
    delete formValues.yearAndMonth;
    onSave(formValues);
  };

  const handleCancel = () => {
    // 重置表单
    totalOutletForm.resetForm();
    // 每次进来需要重置一下表单的操作
    totalOutletForm.setFieldValue({
      id: undefined,
      departmentId: undefined,
      totalOutlet: 0,
      areaId: undefined,
      yearAndMonth: undefined,
    });
    onCancel();
    setTitle(titles[0]);
  };

  return (
    <>
      <BootstrapDialog
        open={open}
        fullWidth
        maxWidth="xs"
        onClose={handleCancel}
        aria-describedby="alert-dialog-slide-description">
        <BootstrapDialogTitle onClose={handleCancel}>
          <Typography variant="h4" component="p">
            {title}
          </Typography>
        </BootstrapDialogTitle>
        <form noValidate onSubmit={totalOutletForm.handleSubmit}>
          <BootstrapContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel
                    htmlFor="area-name"
                    sx={{
                      overflow: "initial",
                      width: "30%",
                    }}>
                    {t("common.common_los_merchant_name")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <ZKSelect
                    placeholder={t("ips.ips_select_merchant")}
                    options={merchantOptions}
                    name="departmentId"
                    labelOptions={{
                      label: "label",
                      value: "value",
                    }}
                    onClear={() => {
                      setAreaData([]);
                      treeSelectRef.current.setItem({
                        id: "",
                        name: "",
                      });
                      totalOutletForm.values.areaId = "";
                      totalOutletForm.setFieldValue("departmentId", "");
                    }}
                    value={totalOutletForm.values.departmentId}
                    onBlur={totalOutletForm.handleBlur}
                    onChange={(e) => {
                      getAreaData(e.target.value);
                      treeSelectRef.current.setItem({
                        id: "",
                        name: "",
                      });
                      totalOutletForm.values.areaId = "";
                      totalOutletForm.handleChange(e);
                    }}
                    error={Boolean(
                      totalOutletForm.touched.departmentId &&
                        totalOutletForm.errors.departmentId
                    )}
                  />
                  {totalOutletForm.touched.departmentId &&
                    totalOutletForm.errors.departmentId && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-departmentId">
                        {totalOutletForm.errors.departmentId}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel
                    htmlFor="area-name"
                    sx={{
                      overflow: "initial",
                      width: "30%",
                    }}>
                    {t("common.common_los_area_name")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <Treeselect
                    ref={treeSelectRef}
                    data={areaData}
                    isClear={false}
                    optionValue="id"
                    optionLabel="name"
                    placeholder={t("ips.ips_enter_region")}
                    onChange={(valuas) => {
                      totalOutletForm.values.areaId = valuas.id;
                    }}
                    onClear={() => {
                      totalOutletForm.values.areaId = undefined;
                    }}
                    error={Boolean(
                      totalOutletForm.touched.areaId &&
                        totalOutletForm.errors.areaId
                    )}
                    disableParent={true}
                  />
                  {totalOutletForm.touched.areaId &&
                    totalOutletForm.errors.areaId && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-areaId">
                        {totalOutletForm.errors.areaId}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel
                    htmlFor="area-name"
                    sx={{
                      overflow: "initial",
                      width: "30%",
                    }}>
                    {t("common.common_year_and_month")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <LocalizationProvider
                    locale={currentLocale}
                    dateAdapter={AdapterDayjs}>
                    <DatePicker
                      openTo="year"
                      views={["year", "month"]}
                      name="yearAndMonth"
                      onBlur={totalOutletForm.handleBlur}
                      value={totalOutletForm?.values?.yearAndMonth}
                      onChange={(value, e) => {
                        totalOutletForm.setFieldValue("yearAndMonth", value);
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          //   helperText={errorMeg}
                          error={Boolean(
                            totalOutletForm.touched.yearAndMonth &&
                              totalOutletForm.errors.yearAndMonth
                          )}
                        />
                      )}
                    />
                  </LocalizationProvider>
                  {totalOutletForm.touched.yearAndMonth &&
                    totalOutletForm.errors.yearAndMonth && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-yearAndMonth">
                        {totalOutletForm.errors.yearAndMonth}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel
                    htmlFor="totalOutlet"
                    sx={{
                      overflow: "initial",
                      width: "30%",
                    }}>
                    {t("common.common_total")}
                    <i style={{ color: "red" }}>*</i>
                  </InputLabel>
                  <OutlinedInput
                    id="totalOutlet"
                    type="number"
                    name="totalOutlet"
                    value={totalOutletForm.values.totalOutlet}
                    onBlur={totalOutletForm.handleBlur}
                    onChange={totalOutletForm.handleChange}
                    placeholder={t("common.common_total_please")}
                    fullWidth
                    error={Boolean(
                      totalOutletForm.touched.totalOutlet &&
                        totalOutletForm.errors.totalOutlet
                    )}
                  />
                  {totalOutletForm.touched.totalOutlet &&
                    totalOutletForm.errors.totalOutlet && (
                      <FormHelperText
                        error
                        id="standard-weight-helper-text-totalOutlet">
                        {totalOutletForm.errors.totalOutlet}
                      </FormHelperText>
                    )}
                </Stack>
              </Grid>
            </Grid>
          </BootstrapContent>
          <BootstrapActions>
            <LoadingButton
              loading={loading}
              disableElevation
              disabled={totalOutletForm.isSubmitting}
              fullWidth
              size="large"
              type="submit"
              variant="contained"
              color="primary">
              {t("table.save")}
            </LoadingButton>
          </BootstrapActions>
        </form>
      </BootstrapDialog>
    </>
  );
};
export default totaloutletForm;
