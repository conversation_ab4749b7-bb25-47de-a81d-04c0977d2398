/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
/* eslint-disable */
import React, { useEffect, useMemo, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import { tableI18n } from "@/utils/tableLang";
import MaterialReactTable from "material-react-table";
import {
  Button,
  Stack,
  Link,
  Grid,
  Box,
  InputLabel,
  TextField,
  OutlinedInput,
  TableCell,
} from "@mui/material";
import DictTag from "@/components/DictTag";
// 消息提示
import { toast } from "react-toastify";
// api
import { useNavigate } from "react-router-dom";
import { listPlayListByPage, removePlayList } from "@/service/api/playList";
import { useConfirm } from "@/components/zkconfirm";
import ShowSelectedMaterial from "./components/showSelectedMaterial";
import PreviewPlayList from "./previewPlayList";
import { useFormik } from "formik";
import { removeEmpty } from "@/utils/StringUtils";
import MainCard from "@/components/MainCard";
import ZKSelect from "@/components/ZKSelect";
import AuthButton from "@/components/AuthButton";
import { playListType, directions } from "@/dict/commonDict";
import { useLocation } from "react-router-dom";

const ProgramScheduleTableList = () => {
  const [isError, setIsError] = useState(false);
  const { t } = useTranslation();
  const confirm = useConfirm();
  const navigate = useNavigate();
  const loaction = useLocation();
  const [rowSelection, setRowSelection] = useState([]);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 分页的参数
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  // 查询参数
  const requestParams = useRef(null);
  const previewRef = useRef(null);

  useEffect(() => {
    // 发请求
    getTableData();
    setRowSelection([]);
  }, [pagination.pageIndex, pagination.pageSize, loaction]);

  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      ...requestParams.current,
    };
    return params;
  };
  const handleRemovePlayList = (ids, names) => {
    confirm({
      title: t("message.messageBox_title"),
      confirmationText: t("common.common_edit_ok"),
      cancellationText: t("common.common_edit_cancel"),
      description: t("common.common_confirm_delete_program", { ids: names }),
    }).then(() => {
      removePlayList(ids).then((res) => {
        toast.success(res.message);
        // 重新请求数据
        getTableData();
        //重置选中行框
        setRowSelection([]);
      });
    });
  };
  // 获取数据
  const getTableData = () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }
    // 开启加载
    setIsLoading(true);
    setIsRefetching(true);
    listPlayListByPage(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };

  //打开预览
  const handleOpenPreview = (playListMaterialList, record) => {
    previewRef.current.handleOpen(playListMaterialList, record?.type);
  };
  const tableInstanceRef = useRef(null);

  const [columsWidth, setColumsWidth] = useState(160); //设置列的宽度
  const [windowWidth, setWindowWidth] = useState(0); //屏幕分辨率大小
  //获取屏幕大小
  const handleWidthData = (value) => {
    // console.log("屏幕宽度", value);
    setWindowWidth(value);
  };

  useEffect(() => {
    let newWidth;
    if (windowWidth < 1000) {
      newWidth = 350;
    } else if (windowWidth < 1200) {
      newWidth = 280;
    } else if (windowWidth < 1550) {
      newWidth = 250;
    } else if (windowWidth < 2000) {
      newWidth = 180;
    } else if (windowWidth > 2400) {
      newWidth = 400;
    } else {
      newWidth = 160;
    }
    setColumsWidth(newWidth);
  }, [windowWidth]);

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        enableColumnActions: false,
        enableSorting: false,
        header: t("ips.ips_playlist_name"),
        size: 100,
      },
      {
        header: t("ips.ips_selected_advertisements"),
        enableColumnActions: false,
        enableSorting: false,
        size: columsWidth,

        Cell: ({ row }) => {
          let playListMaterialList = row.original.playListMaterialList;
          return (
            <Box
              style={{
                overflow: "visible",
                position: "relative",
              }}>
              <ShowSelectedMaterial
                playListMaterialList={playListMaterialList}
                windowWidthData={handleWidthData}
                columsWidth={columsWidth}
              />
            </Box>
          );
        },
      },
      {
        accessorKey: "duration",
        header: t("ips.ips_total_duration") + "(s)",
        size: 100,
        enableColumnActions: false,
        enableSorting: false,
      },
      {
        accessorKey: "type",
        header: t("ips.ips_playlist_type"),
        size: 100,
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={playListType}
              value={row.original.type}
              fieldName={{ title: "label" }}
            />
          );
        },
      },
      {
        accessorKey: "direction",
        header: t("common.common_direction"),
        enableColumnActions: false,
        enableSorting: false,
        size: 70,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={directions}
              fieldName={{ title: "label", value: "value" }}
              value={row.original.direction}
            />
          );
        },
      },
    ],
    [columsWidth]
  );
  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      name: "",
      type: "",
      direction: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        // 拷贝value值
        const tempValue = { ...values };
        await removeEmpty(tempValue);
        // setRequestParams(tempValue);
        requestParams.current = tempValue;
        setPagination({
          pageIndex: 0,
          pageSize: 10,
        });
        getTableData();
        // 查询table
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });
  // 重置查询
  const resetQuery = () => {
    // 重置表单
    queryFormik.resetForm();
    setPagination({
      pageIndex: 0,
      pageSize: 10,
    });
    requestParams.current = null;
    getTableData();
  };
  return (
    <>
      <MainCard style={{ marginBottom: "10px" }}>
        <form noValidate onSubmit={queryFormik.handleSubmit}>
          <Grid
            container
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            spacing={5}>
            <Grid item xs={6} sm={4} md={3}>
              <TextField
                label={t("ips.ips_playlist_name")}
                value={queryFormik.values.name}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                size="small"
                type="text"
                name="name"
                fullWidth
                placeholder={t("common.common_input_name")}
              />
            </Grid>
            <Grid item xs={6} sm={4} md={3}>
              <ZKSelect
                name="type"
                size="small"
                value={queryFormik.values.type}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                options={playListType}
                onClear={() => {
                  queryFormik.setFieldValue("type", "");
                }}
                placeholder={t("common.common_select_type")}
                menuWidth={200}
              />
            </Grid>
            <Grid item xs={6} sm={4} md={3}>
              <ZKSelect
                size="small"
                name="direction"
                value={queryFormik.values.direction}
                onChange={queryFormik.handleChange}
                onBlur={queryFormik.handleBlur}
                options={directions}
                onClear={() => {
                  queryFormik.setFieldValue("direction", "");
                }}
                placeholder={t("ips.ips_please_select_direction")}
                menuWidth={200}
              />
            </Grid>
            <Grid
              item
              xs={6}
              sm={4}
              md={3}
              direction="row"
              justifyContent="flex-end">
              <Stack
                direction="row"
                justifyContent="flex-start"
                alignItems="flex-start"
                spacing={2}>
                <Button
                  disableElevation
                  type="submit"
                  variant="contained"
                  size="small">
                  {t("common.common_table_query")}
                </Button>
                <Button
                  variant="outlined"
                  onClick={resetQuery}
                  color="info"
                  size="small"
                  sx={{
                    minWidth: "90px",
                  }}>
                  {t("common.common_op_reset")}
                </Button>
              </Stack>
            </Grid>
          </Grid>
        </form>
      </MainCard>
      <MaterialReactTable
        tableInstanceRef={tableInstanceRef}
        // table 状态
        state={{
          // 加载状态
          isLoading,
          // 分页参数
          pagination,
          // 重新拉取
          showProgressBars: isRefetching,
          showAlertBanner: isError,

          rowSelection,
        }}
        renderToolbarInternalActions={({ table }) => <></>}
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",
            border: "1px solid #f0f0f0",
          },
        }}
        muiTableHeadRowProps={{
          sx: { backgroundColor: "#fafafa", boxShadow: "none" },
        }}
        // 关闭排序
        enableSorting={false}
        // 布局方式
        layoutMode="grid"
        // 开启列对齐
        muiTableHeadCellProps={{
          sx: {
            "& .Mui-TableHeadCell-Content": {
              justifyContent: "space-between",
            },
          },
        }}
        // 解决列太多宽度太长问题
        enableColumnResizing
        // enablePinning
        // 初始化状态
        initialState={{ columnVisibility: { createTime: true } }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: t("table.loading_error"),
              }
            : undefined
        }
        //行选中
        onRowSelectionChange={setRowSelection}
        muiTableBodyRowProps={({ row }) => ({
          onClick: row.getToggleSelectedHandler(),
          sx: { cursor: "pointer" },
        })}
        // 开启多选
        enableRowSelection
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "610px" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{ sx: { backgroundColor: "white" } }}
        muiTableProps={{
          sx: { backgroundColor: "white", tableLayout: "fixed" },
        }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        // 分页回调函数
        onPaginationChange={setPagination}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        manualPagination
        manualSorting
        // 开启分页
        enablePagination
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        // localization={{
        //   actions: "操作栏",
        //   and: "e",
        //   cancel: "取消",
        //   changeFilterMode: "过滤",
        //   changeSearchMode: "搜索",
        //   clearFilter: "删除过滤",
        //   clearSearch: "清空搜索",
        //   clearSort: "清除排序",
        //   clickToCopy: "点击复制",
        //   showHideSearch: "显示搜索",
        //   // ... and many more - see link below for full list of translation keys
        // }}
        localization={tableI18n}
        // 自定义表头按钮
        renderTopToolbarCustomActions={({ table }) => {
          const handleDeactivate = () => {
            table.getSelectedRowModel().flatRows.map((row) => {
              alert("deactivating " + row.getValue("name"));
            });
          };

          const handleActivate = () => {
            table.getSelectedRowModel().flatRows.map((row) => {
              alert("activating " + row.getValue("name"));
            });
          };

          const handleContact = () => {
            table.getSelectedRowModel().flatRows.map((row) => {
              alert("contact " + row.getValue("name"));
            });
          };

          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <AuthButton button="sd:playlist:playlist:save">
                <Button
                  variant="contained"
                  onClick={() => navigate("/playList/playList/add")}>
                  {t("ips.ips_new_playlist")}
                </Button>
              </AuthButton>
              <AuthButton button="sd:playlist:playlist:delete">
                <Button
                  color="secondary"
                  // disabled
                  disabled={
                    !table.getIsSomeRowsSelected() &&
                    !table.getIsAllRowsSelected()
                  }
                  onClick={() => {
                    const ids = [];
                    const names = [];
                    table.getSelectedRowModel().rows.map((row) => {
                      ids.push(row.original.id);
                      names.push(row.original.name);
                    });
                    handleRemovePlayList(ids, names);
                  }}
                  variant="contained">
                  {t("common.common_op_batch_del")}
                </Button>
              </AuthButton>
            </Stack>
          );
        }}
        // 多选底部提示
        positionToolbarAlertBanner="none"
        // 开启action操作
        enableRowActions
        // action操作位置
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            header: t("common.common_relatedOp"), //change header text
            size: 120, //make actions column wider
          },
        }}
        renderRowActions={({ table, row }) => (
          <Stack direction="row" spacing={1} alignItems="center">
            <Link
              component="button"
              underline="none"
              onClick={() =>
                handleOpenPreview(
                  row.original.playListMaterialList,
                  row.original
                )
              }>
              {t("common.common_op_preview")}
            </Link>
            <AuthButton button="sd:playlist:playlist:update">
              <Link
                component="button"
                underline="none"
                onClick={() =>
                  navigate(`/playlist/editPlayList?id=${row.original.id}`)
                }>
                {t("common.common_op_edit")}
              </Link>
            </AuthButton>
            <AuthButton button="sd:playlist:playlist:delete">
              <Link
                color="error"
                component="button"
                underline="none"
                onClick={() => {
                  handleRemovePlayList(row.original.id, row.original.name);
                }}>
                {t("common.common_op_del")}
              </Link>
            </AuthButton>
          </Stack>
        )}
      />
      <PreviewPlayList ref={previewRef} />
    </>
  );
};

export default ProgramScheduleTableList;
