import request from "@/utils/request";
/**
 *
 * @param {获取零售商分页列表} params
 * @returns
 */
export const getPrincipaList = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/principal/selected`,
    method: "get",
    params: params,
  });
};

/**
 *
 * @param {获取区域下拉列表} params
 * @returns
 */
export const getAreaTree = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/area/tree/select`,
    method: "GET",
    params: params,
  });
};

export const getOutletList = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/outlet/query/list`,
    method: "GET",
    params: params,
  });
};


export const selectedOutlet = (departmentId) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/outlet/selected`,
    method: "GET",
    params: {
      departmentId
    },
  })
}

/**
 *   获取路由菜单接口
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getUserMenus = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/auth/resource/menu`,
    method: "GET",
    params: params,
  });
};


export const filterOutlets = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/outlet/selected`,
    method: "GET",
    params: params,
  })
}

export const getOutletType = () => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/outlet/selected`,
    method: "GET",
  })
}

/**
 * 获取登录的用户信息
 * @param {} params
 */
export const getLoginInfor = () => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/user/user_info`,
    method: "GET",
  });
};



/**
 *  获取按钮级权限
 */

export const getAuthButton = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/auth/application/${params}`,
    method: "POST",

  });
};