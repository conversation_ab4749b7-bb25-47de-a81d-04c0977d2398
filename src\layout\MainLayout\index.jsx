import React from "react";
import { useEffect, useState } from "react";
import { Outlet } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import LoadMapScrrip from "@/LoadMapScript";
// material-ui
import { useTheme } from "@mui/material/styles";
import { Box, Button, Divider, Toolbar, useMediaQuery } from "@mui/material";

// project import
import Drawer from "./Drawer";
import Header from "./Header";
// import Header from "./Header";
// import navigation from "menu-items";
import Breadcrumbs from "@/components/@extended/Breadcrumbs";
import SimpleBar from "@/components/third-party/SimpleBar";
import Logo from "@/components/Logo/Logo";
import SvgIcon from "@/components/SvgIcon";

// types
import { openDrawer } from "@/store/reducers/menu";
import { Backdrop, CircularProgress } from "@mui/material";
// ==============================|| MAIN LAYOUT ||============================== //
import { useDispatchMenu } from "@/hooks/menu";
import { useStateUserInfo, useDispatchUser } from "@/hooks/user";
import bgUrl from "@/assets/images/bg/GlobalBg.png?react";
import LangSwitch from "./Header/HeaderContent/LangSwitch";
import About from "./Header/HeaderContent/About";
import Callback from "./Header/HeaderContent/Callback";
const MainLayout = () => {
  const theme = useTheme();
  const matchDownLG = useMediaQuery(theme.breakpoints.down("xl"));
  const dispatch = useDispatch();
  const { stateSetMenuList } = useDispatchMenu();
  const { drawerOpen, backdropOpen } = useSelector((state) => state.menu);
  const { stateSetUser, stateSetPermission } = useDispatchUser();
  const [loading, setLoading] = useState(false);
  // drawer toggler
  const [open, setOpen] = useState(drawerOpen);
  const handleDrawerToggle = () => {
    setOpen(!open);
    dispatch(openDrawer({ drawerOpen: !open }));
  };

  // set media wise responsive drawer
  useEffect(() => {
    setOpen(!matchDownLG);
    dispatch(openDrawer({ drawerOpen: !matchDownLG }));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [matchDownLG]);

  useEffect(() => {
    if (open !== drawerOpen) setOpen(drawerOpen);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [drawerOpen]);

  const handleBackToMain = () => {
    window.location.href = "/application/center"; // 跳转到主应用的根路径
  };

  return (
    <>
      <LoadMapScrrip />
      <div
        className="relative h-[100vh] flex flex-col w-full overflow-y-hidden"
        style={{
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
          // overflow: "auto",
          borderSizing: "border-box",
          backgroundAttachment: "fixed",
          backgroundImage: `url(${bgUrl})`,
        }}>
        <div
          className="w-full h-[60px] flex items-center bg-white fixed px-4 justify-between"
          style={{
            boxShadow: "0 0 8px 0 rgba(0,0,0,.1)",
            zIndex: 1,
          }}>
          <div
            className="flex items-center justify-center align-middle gap-4"
            style={{
              marginLeft: "50px",
            }}>
            {/* <div
              onClick={handleBackToMain}
              className="hover:bg-gray-200 w-[40px] text-primary-500 h-[40px] border border-solid border-gray-100 flex items-center justify-center rounded-lg ">
              <SvgIcon icon="weui:back-filled" height="30px" />
            </div> */}
            <Logo />
          </div>
          <div className="flex flex-row gap-2 items-center">
            <Callback></Callback>
            <LangSwitch />
            <About />
          </div>
        </div>
        {/* <Header open={open} handleDrawerToggle={handleDrawerToggle} /> */}
        <div className="flex w-full h-full flex-row gap-2 mt-[60px] overflow-hidden ">
          <div className="relative  h-full">
            <div className={`h-full ${open ? "w-[230px]" : "w-0"}`}>
              <Drawer
                className={`bg-white h-full  shadow-sm border-gray-100 transition-all duration-300 ease-in-out ${
                  open ? "w-[230px]" : "w-0"
                }`}
                open={open}
                handleDrawerToggle={handleDrawerToggle}
              />
            </div>
          </div>

          <div className="w-full flex flex-1 overflow-hidden">
            <SimpleBar
              sx={{
                "& .simplebar-content": {
                  display: "flex",
                  flexDirection: "column",
                },
              }}>
              <div className="w-full   py-2">
                <Outlet />
              </div>
            </SimpleBar>
          </div>
        </div>
      </div>
    </>
  );
};

export default MainLayout;
