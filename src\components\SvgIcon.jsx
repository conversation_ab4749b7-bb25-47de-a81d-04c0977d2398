import { Icon } from "@iconify/react";
// import type { CSSProperties } from 'react';

// interface Props {
//   readonly className?: string;
//   /** Iconify icon name */
//   readonly icon?: string;
//   /** Local svg icon name */
//   readonly localIcon?: string;
//   readonly style?: CSSProperties;
// }

const defaultLocalIcon = "no-icon";
const { VITE_ICON_LOCAL_PREFIX: prefix } = import.meta.env;
const symbolId = (localIcon = defaultLocalIcon) => {
  const iconName = localIcon || defaultLocalIcon;
  return `#${prefix}-${iconName}`;
};
const SvgIcon = ({ icon, localIcon, width, height, ...props }) => {
  /** If localIcon is passed, render localIcon first */
  return localIcon || !icon ? (
    <svg
      height={height ? height : "1em"}
      width={width ? width : "1em"}
      {...props}
      aria-hidden="true">
      <use fill="currentColor" href={symbolId(localIcon)} />
    </svg>
  ) : (
    <Icon
      icon={icon}
      height={height ? height : "1em"}
      width={width ? width : "1em"}
      {...props}
    />
  );
};

export default SvgIcon;
